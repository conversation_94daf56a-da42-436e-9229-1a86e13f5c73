package edu.hbp.he.useractivity.security;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpMethod;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.annotation.web.configurers.HeadersConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.UrlBasedCorsConfigurationSource;
import org.springframework.web.filter.CorsFilter;

import edu.hbp.he.security.filter.ImpersonationUserFilter;
import edu.hbp.he.security.service.ImpersonationUserService;

@Configuration
@EnableWebSecurity
public class SecurityConfig {

  @Autowired
  private ImpersonationUserService impersonationUserService;

  public SecurityConfig() {
    super();
  }
  
  @Bean
  public SecurityFilterChain configure(HttpSecurity http) throws Exception {
    http.csrf(AbstractHttpConfigurer::disable)
        .headers(headers -> headers
            .frameOptions(HeadersConfigurer.FrameOptionsConfig::disable)
            .xssProtection(HeadersConfigurer.XXssConfig::disable));
    setupAnonymousUrls(http);
    setupAuthorizeUrls(http);
    setupFilters(http);
    return http.build();
  }

  private void setupAnonymousUrls(HttpSecurity http) throws Exception {
    http.authorizeHttpRequests((authz) -> authz
        .requestMatchers(HttpMethod.GET, "/admin/health").anonymous()
        .requestMatchers(HttpMethod.GET, "/admin/info").anonymous()
        .requestMatchers(HttpMethod.GET, "/webjars/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/swagger-ui.html").anonymous()
        .requestMatchers(HttpMethod.GET, "/swagger-ui/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/configuration/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/swagger-resources/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/v3/api-docs/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/v2/api-docs/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/hello/**").anonymous()
        .requestMatchers(HttpMethod.GET, "/log-message").anonymous());
  }

  private void setupAuthorizeUrls(HttpSecurity http) throws Exception {
    http.authorizeHttpRequests((authz) -> authz
        .requestMatchers("/**").fullyAuthenticated());
    http.oauth2ResourceServer(httpSecurityOAuth2ResourceServerConfigurer
        -> httpSecurityOAuth2ResourceServerConfigurer.opaqueToken(Customizer.withDefaults()));
  }

  private void setupFilters(HttpSecurity http) throws Exception {
    http.sessionManagement(session -> session.sessionCreationPolicy(SessionCreationPolicy.STATELESS));
    http.addFilterAfter(new ImpersonationUserFilter(impersonationUserService),
        BasicAuthenticationFilter.class);
  }

  @Bean
  CorsFilter corsFilter() {
    final UrlBasedCorsConfigurationSource source = new UrlBasedCorsConfigurationSource();
    final CorsConfiguration config = new CorsConfiguration();
    config.addAllowedOrigin("*");
    config.addAllowedHeader("*");
    config.addAllowedMethod("OPTIONS");
    config.addAllowedMethod("HEAD");
    config.addAllowedMethod("GET");
    config.addAllowedMethod("PUT");
    config.addAllowedMethod("POST");
    config.addAllowedMethod("DELETE");
    config.addAllowedMethod("PATCH");
    config.applyPermitDefaultValues();
    source.registerCorsConfiguration("/**", config);
    return new CorsFilter(source);
  }
}
