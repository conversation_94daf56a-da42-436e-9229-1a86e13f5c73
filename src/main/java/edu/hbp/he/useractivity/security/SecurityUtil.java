package edu.hbp.he.useractivity.security;

import java.util.Collection;
import java.util.Collections;
import java.util.Set;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.web.authentication.switchuser.SwitchUserGrantedAuthority;
import org.springframework.stereotype.Component;

import edu.hbp.he.security.domain.CustomAttribute;
import edu.hbp.he.security.domain.User;
import edu.hbp.he.security.util.CoreSecurityUtil;

@Component
public class SecurityUtil {

  public SecurityUtil() {
    super();
    SecurityContextHolder.setStrategyName(SecurityContextHolder.MODE_INHERITABLETHREADLOCAL);
  }

  public static User getCurrentUser() {
    return CoreSecurityUtil.getCurrentUser();
  }

  public static boolean hasAnyRoles(String role) {
    return getAuthorities().contains(role);
  }

  private static Set<String> getAuthorities() {
    Collection<? extends GrantedAuthority> authorities =
        SecurityContextHolder.getContext().getAuthentication().getAuthorities();
    if (CollectionUtils.isNotEmpty(authorities)) {
      return AuthorityUtils.authorityListToSet(authorities);
    }
    return Collections.emptySet();
  }

  public static boolean isHbsUser() {
    return isHbsPartialUser() || isHbsPlusUser();
  }

  public static boolean isHbsPartialUser() {
    return hasPermission("PERM_ACCESS_HBS_CONTENT");
  }

  public static boolean isHbsPlusUser() {
    return hasPermission("PERM_ACCESS_HBS_CONTENT_PLUS");
  }

  /**
   * The token should be used to make rest calls between he services to support token based impersonation.
   *
   * @return
   */
  public static String getCurrentUserAccessTokenOrConciergeToken() {
    String accessToken = getCurrentUserAccessToken();
    if (accessToken == null) {
      return getConciergeToken();
    }
    return accessToken;
  }

  public static String getCurrentUserAccessToken() {
    return CoreSecurityUtil.getAccessToken();
  }

  /**
   * Returns the token of a Concierged user.
   *
   * @return
   */
  private static String getConciergeToken() {
    if (SecurityContextHolder.getContext() != null
        || SecurityContextHolder.getContext().getAuthentication() != null
        || SecurityContextHolder.getContext().getAuthentication().getAuthorities() != null) {

      Collection<? extends GrantedAuthority> authorities
          = SecurityContextHolder.getContext().getAuthentication().getAuthorities();

      GrantedAuthority impersonatorAuthority = authorities.stream()
          .filter(authority -> authority instanceof SwitchUserGrantedAuthority)
          .findAny().orElse(null);

      if (impersonatorAuthority != null) {
        return CoreSecurityUtil.getAccessToken();
      }
    }
    return null;
  }

  public static String getPreviewStatus(User user) {
    String previewStatus = "previewStatus";
    return getCustomAttributeValue(user, previewStatus);
  }

  private static String getCustomAttributeValue(User user, String customAttributeName) {
    String customAttributeValue = null;
    if (user.getProfile() != null && CollectionUtils.isNotEmpty(user.getProfile().getCustomAttributes())) {
      customAttributeValue = user.getProfile().getCustomAttributes().stream()
          .filter(customAttribute -> customAttribute.getName().equalsIgnoreCase(customAttributeName))
          .findFirst().orElseGet(CustomAttribute::new).getValue();
    }
    return customAttributeValue;
  }

  public static String getPreviewStatusToNormalDate(User user) {
    String previewStatusToNormalDate = "previewStatusToNormalDate";
    return getCustomAttributeValue(user, previewStatusToNormalDate);
  }

  public static String getExpirationDate(User user) {
    String expirationDateAttrName = "expirationDate";
    return getCustomAttributeValue(user, expirationDateAttrName);
  }

  public static String getInstitutionId(User user) {
    String institutionId = "institutionId";
    return getCustomAttributeValue(user, institutionId);
  }

  public static String getInstitutionName(User user) {
    String institutionName = "institutionName";
    return getCustomAttributeValue(user, institutionName);
  }

  public static boolean isContentAdminUser() {
    if (hasPermission("PERM_CONCIERGE") || hasPermission("PERM_MANAGE_FORIO_PRODUCT")) {
      return true;
    }
    return false;
  }

  public static boolean hasPermission(String grantedAuthority) {
    return getAuthorities().contains(grantedAuthority);
  }

  public static boolean isImpersonating() {
    return CoreSecurityUtil.isImpersonating();
  }

  public static boolean isCourseCreator() {
    return hasPermission("PERM_CREATE_COURSE");
  }

  public static boolean isAnonymousUser() {
    User user = getCurrentUser();
    return user.getUserId() == null;
  }

}