package edu.hbp.he.useractivity.domain;

import java.io.Serializable;
import java.sql.Timestamp;

import org.hibernate.annotations.Where;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.GenerationType;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
@Entity
@Table(name = "ITEM_ACCESS_DETAILS")
@Where(clause = "deleted='N'")
public class ItemAccessDetails implements Serializable {
  private static final long serialVersionUID = -4756982868507096215L;

  private Long itemAccessDetailsId;
  private Long userId;
  private String itemId; // this could be availability id, coursepack id, or collection id
  private String itemType; // this could be PRODUCT, COURSE or COLLECTION
  private String itemTitle;
  private Timestamp itemAccessedDate;
  private Timestamp createdDate;
  private String deleted = "N";

  @Id
  @GeneratedValue(strategy = GenerationType.IDENTITY)
  @Column(name = "ITEM_ACCESS_DETAILS_ID", unique = true, nullable = false, precision = 10)
  public Long getItemAccessDetailsId() {
    return itemAccessDetailsId;
  }

  public void setItemAccessDetailsId(Long itemAccessDetailsId) {
    this.itemAccessDetailsId = itemAccessDetailsId;
  }

  @Column(name = "USER_ID")
  public Long getUserId() {
    return userId;
  }

  public void setUserId(Long userId) {
    this.userId = userId;
  }

  @Column(name = "ITEM_ID")
  public String getItemId() {
    return itemId;
  }

  public void setItemId(String itemId) {
    this.itemId = itemId;
  }

  @Column(name = "ITEM_TYPE")
  public String getItemType() {
    return itemType;
  }

  public void setItemType(String itemType) {
    this.itemType = itemType;
  }

  @Column(name = "ITEM_TITLE")
  public String getItemTitle() {
    return itemTitle;
  }

  public void setItemTitle(String itemTitle) {
    this.itemTitle = itemTitle;
  }

  @Column(name = "ITEM_ACCESSED_DATE")
  public Timestamp getItemAccessedDate() {
    return itemAccessedDate;
  }

  public void setItemAccessedDate(Timestamp itemAccessedDate) {
    this.itemAccessedDate = itemAccessedDate;
  }

  @Column(name = "CREATED_DATE")
  public Timestamp getCreatedDate() {
    return createdDate;
  }

  public void setCreatedDate(Timestamp createdDate) {
    this.createdDate = createdDate;
  }

  @JsonIgnore
  @Column(name = "DELETED")
  public String getDeleted() {
    return deleted;
  }

  public void setDeleted(String deleted) {
    this.deleted = deleted;
  }
}
