package edu.hbp.he.useractivity.constants;

public enum ByteArrayContentType {
  PDF("application/pdf"),
  XLSX("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
  XLS("application/vnd.ms-excel"),
  SPREADSHEET("application/vnd.ms-excel"),
  PPTX("application/vnd.openxmlformats-officedocument.presentationml.presentation"),
  PPT("application/vnd.ms-powerpoint"),
  POWERPOINT("application/vnd.ms-powerpoint"),
  DOCX("application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
  DOC("application/msword"),
  OCTET("application/octet-stream"),
  MP3("audio/mp3"),
  MPEG("audio/mpeg"),
  CSV("text/csv"),
  TEXT_PLAIN("text/plain"),
  P<PERSON>("image/png"),
  <PERSON><PERSON>("image/jpeg"),
  G<PERSON>("image/gif"),
  EPUB("application/epub+zip");

  private String description;

  ByteArrayContentType(String description) {
    this.description = description;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }

  public static ByteArrayContentType fromName(String name) {
    ByteArrayContentType bct = null;
    try {
      bct = ByteArrayContentType.valueOf(name);
    } catch (Exception ex) {
      System.out.println(ex.getMessage());
    }
    return bct;
  }

  public static ByteArrayContentType fromDescription(String description) {
    ByteArrayContentType bct = null;
    ByteArrayContentType[] values = ByteArrayContentType.values();
    for (ByteArrayContentType byteContentType : values) {
      if (byteContentType.getDescription().equalsIgnoreCase(description)) {
        bct = byteContentType;
        break;
      }
    }
    return bct;
  }
}