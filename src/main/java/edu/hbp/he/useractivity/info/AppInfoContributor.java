package edu.hbp.he.useractivity.info;

import org.springframework.boot.SpringBootVersion;
import org.springframework.boot.actuate.info.Info;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.stereotype.Component;

@Component
public class AppInfoContributor implements InfoContributor {
  @Override
  public void contribute(Info.Builder builder) {
    builder.withDetail("spring-boot.version", SpringBootVersion.getVersion());
    builder.withDetail("java.version", System.getProperty("java.version"));
  }
}