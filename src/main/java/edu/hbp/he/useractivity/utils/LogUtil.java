package edu.hbp.he.useractivity.utils;

import org.slf4j.Logger;

import edu.hbp.he.useractivity.security.SecurityUtil;

/**
 * <AUTHOR>
 */
public class LogUtil {


  public LogUtil() {

  }

  public static void logError(Logger logger, Exception exception) {
    String logging = "Anonymous";
    if (SecurityUtil.getCurrentUser() != null && SecurityUtil.getCurrentUser().getUsername() != null) {
      logging = SecurityUtil.getCurrentUser().getUserId() + " : " + SecurityUtil.getCurrentUser().getUsername();
    }

    StringBuilder sb = new StringBuilder(logging);
    sb.append(" : ").append(exception.getMessage());
    logger.error(sb.toString(), exception);
  }

  public static void logWarning(Logger logger, Exception exception) {
    String logging = "Anonymous";
    if (SecurityUtil.getCurrentUser() != null && SecurityUtil.getCurrentUser().getUsername() != null) {
      logging = SecurityUtil.getCurrentUser().getUserId() + " : " + SecurityUtil.getCurrentUser().getUsername();
    }

    StringBuilder sb = new StringBuilder(logging);
    sb.append(" : ").append(exception.getMessage());
    logger.error(sb.toString(), exception);
  }

}
