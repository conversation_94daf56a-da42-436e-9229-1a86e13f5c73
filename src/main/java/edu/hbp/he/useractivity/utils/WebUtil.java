package edu.hbp.he.useractivity.utils;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.codec.binary.Base64;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;


/**
 * <AUTHOR>
 */
public class WebUtil {

  public WebUtil() {
    super();
  }

  public static String getBasicAuthorizationHeader(String clientId, String clientSecret) {
    String headerValue = "Basic " + encodeBase64(clientId, clientSecret);
    return headerValue;
  }

  public static String encodeBase64(String clientId, String clientSecret) {
    String value = clientId + ":" + clientSecret;
    byte[] byteContent = Base64.encodeBase64(value.getBytes());
    return new String(byteContent);
  }

  public static String getBearerAuthorizationHeader(String token) {
    String headerValue = "Bearer " + token;
    return headerValue;
  }

  public static HttpHeaders getRequestHttpHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Accept", "application/json");
    return headers;
  }

  public static HttpHeaders getResponseHttpHeadersAsXml() {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Content-Type", "text/xml;charset=UTF-8");
    List<MediaType> acceptableMediaTypes = new ArrayList<MediaType>();
    acceptableMediaTypes.add(MediaType.TEXT_XML);
    headers.setAccept(acceptableMediaTypes);
    return headers;
  }

  public static <T> ResponseEntity<T> getBadRequestResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.BAD_REQUEST);
    return httpResponse;
  }

  public static HttpHeaders getResponseHttpHeaders() {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Content-Type", "application/json");
    List<MediaType> acceptableMediaTypes = new ArrayList<MediaType>();
    acceptableMediaTypes.add(MediaType.APPLICATION_JSON);
    headers.setAccept(acceptableMediaTypes);
    return headers;
  }

  public static <T> ResponseEntity<T> getResourceNotFoundResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.NOT_FOUND);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getInternalServerErrorResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.INTERNAL_SERVER_ERROR);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getOkResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.OK);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getCreatedResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.CREATED);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getDeleteResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.NO_CONTENT);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getDeleteResponse() {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(null, headers, HttpStatus.NO_CONTENT);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getNoContentResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.NO_CONTENT);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getResponse(T responseBody, HttpStatus status) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, status);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getResponse(T responseBody, Integer statusCode) {
    HttpHeaders headers = getResponseHttpHeaders();
    HttpStatus status = HttpStatus.valueOf(statusCode);
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, status);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getResponse(T responseBody, HttpHeaders headers, HttpStatus status) {
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, status);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getUnauthorizedResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.UNAUTHORIZED);
    return httpResponse;
  }

  public static <T> ResponseEntity<T> getForbiddenResponse(T responseBody) {
    HttpHeaders headers = getResponseHttpHeaders();
    ResponseEntity<T> httpResponse = new ResponseEntity<T>(responseBody, headers, HttpStatus.FORBIDDEN);
    return httpResponse;
  }

  public static ResponseEntity<String> get301RedirectResponse(String redirectUrl) {
    HttpHeaders headers = getResponseHttpHeadersAsHtml();
    headers.add("Location", redirectUrl);
    ResponseEntity<String> httpResponse = new ResponseEntity<String>(null, headers, HttpStatus.MOVED_PERMANENTLY);
    return httpResponse;
  }

  public static ResponseEntity<String> getHtmlResponse(String responseBody) {
    HttpHeaders headers = getResponseHttpHeadersAsHtml();
    ResponseEntity<String> httpResponse = new ResponseEntity<String>(responseBody, headers, HttpStatus.OK);
    return httpResponse;
  }

  public static HttpHeaders getResponseHttpHeadersAsHtml() {
    HttpHeaders headers = new HttpHeaders();
    headers.set("Content-Type", "text/html;charset=UTF-8");
    List<MediaType> acceptableMediaTypes = new ArrayList<MediaType>();
    acceptableMediaTypes.add(MediaType.TEXT_HTML);
    headers.setAccept(acceptableMediaTypes);
    return headers;
  }

  public static HttpHeaders getHttpHeader(MediaType mediaType) {
    List<MediaType> acceptableMediaTypes = new ArrayList<MediaType>();
    acceptableMediaTypes.add(mediaType);
    HttpHeaders headers = new HttpHeaders();
    headers.setAccept(acceptableMediaTypes);
    headers.setContentType(mediaType);
    return headers;
  }

}
