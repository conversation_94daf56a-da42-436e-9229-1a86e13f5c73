package edu.hbp.he.useractivity.utils;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.MessageSource;
import org.springframework.stereotype.Component;
import org.springframework.validation.BeanPropertyBindingResult;
import org.springframework.validation.FieldError;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;

import edu.hbp.he.useractivity.validation.DataValidationError;
import edu.hbp.he.useractivity.validation.ValidationResult;
import jakarta.validation.ConstraintViolation;

/**
 * <AUTHOR>
 */
@Component(value = "useractivityValidationUtil")
public class ValidationUtil implements ApplicationContextAware {
  private static Validator validator;
  private static MessageSource messageSource;
  @Autowired
  private ApplicationContext applicationContext;

  public static void setValidator(Validator validator) {
    ValidationUtil.validator = validator;
  }

  public static void setMessageSource(MessageSource messageSource) {
    ValidationUtil.messageSource = messageSource;
  }

  /**
   * Validate all properties of an object including nested object properties. The object to be validated
   * must be using annotations.
   *
   * @param object
   * @return ValidationResult
   */
  public static ValidationResult validateAllProperties(Object object) {
    ValidationResult validationResult = new ValidationResult();
    if (object == null) {
      return validationResult;
    }

    String className = object.getClass().getSimpleName();
    BeanPropertyBindingResult bindResult = new BeanPropertyBindingResult(object, className);
    validator.validate(object, bindResult);//Validates all properties of the object and nested object

    if (bindResult.hasErrors()) {
      List<FieldError> errors = bindResult.getFieldErrors();
      String errorMessage;
      for (FieldError error : errors) {
        errorMessage = getErrorMessage(error, messageSource, className);
        DataValidationError ve = new DataValidationError();
        ve.setFieldName(error.getField());
        ve.setErrorCode(getErrorCode(error, className));
        ve.setErrorMessage(errorMessage);
        validationResult.addError(ve);
      }
    }
    return validationResult;
  }

  /**
   * Validate the properties of an object specified in properties parameter.
   *
   * @param object
   * @param properties
   * @return ValidationResult
   */
  public static ValidationResult validate(Object object, String[] properties) {
    ValidationResult validationResult = new ValidationResult();
    validationResult = validate(object, properties, validationResult);
    return validationResult;
  }

  /**
   * Validate the properties of an object specified in properties parameter.
   *
   * @param object
   * @param properties
   * @return ValidationResult
   */
  public static ValidationResult validate(Object object, String[] properties, ValidationResult validationResult) {
    if (object == null) {
      return validationResult;
    }

    String className = object.getClass().getSimpleName();
    LocalValidatorFactoryBean lValidator = (LocalValidatorFactoryBean) validator;
    Set<ConstraintViolation<?>> errors = new HashSet<ConstraintViolation<?>>();

    for (String property : properties) {
      errors.addAll(lValidator.validateProperty(object, property));//Validates only the specified properties
    }

    if (errors.size() > 0) {
      String errorMessage;
      for (ConstraintViolation<?> constraint : errors) {
        errorMessage = getErrorMessage(messageSource, constraint, className);
        DataValidationError ve = new DataValidationError();
        ve.setFieldName(constraint.getPropertyPath().toString());
        ve.setErrorCode(getErrorCode(constraint, className));
        ve.setErrorMessage(errorMessage);
        validationResult.addError(ve);
      }
    }

    return validationResult;
  }

  /**
   * This is non ApplicationContextAware interface method.
   *
   * @param context
   * @param propertiesMap
   * @return ValidationResult
   */
  public static ValidationResult validate(String context, Map<String, Object> propertiesMap) {
    ValidationResult validationResult = new ValidationResult();
    if (propertiesMap == null) {
      return validationResult;
    }

    List<String> errors = new ArrayList<String>();
    for (Entry<String, Object> entry : propertiesMap.entrySet()) {
      if (StringUtils.isEmpty((String) entry.getValue())) {
        errors.add("Empty." + context + "." + entry.getKey());
      }
    }

    if (errors.size() > 0) {
      String errorMessage;
      for (String errorKey : errors) {
        errorMessage = getErrorMessage(messageSource, errorKey);
        DataValidationError ve = new DataValidationError();
        ve.setFieldName("???");
        ve.setErrorMessage(errorMessage);
        validationResult.addError(ve);
      }
    }
    return validationResult;
  }

  private static String getErrorCode(ConstraintViolation<?> constraint, String className) {
    StringBuilder errorKey = new StringBuilder();

    String constraintName = getConstraintName(constraint.getMessageTemplate());
    errorKey.append(constraintName);
    errorKey.append(".");
    errorKey.append(className);
    errorKey.append(".");
    errorKey.append(constraint.getPropertyPath().toString());
    return errorKey.toString().trim();
  }

  private static String getErrorCode(FieldError error, String className) {
    StringBuilder errorKey = new StringBuilder();

    errorKey.append(error.getCode());
    errorKey.append(".");
    errorKey.append(className);
    errorKey.append(".");
    errorKey.append(error.getField());
    return errorKey.toString().trim();
  }

  private static String getErrorMessage(FieldError error, MessageSource messageSource, String className) {
    String errorKey = getErrorCode(error, className);
    return messageSource.getMessage(errorKey, null, Locale.ENGLISH);
  }

  public static String getErrorMessage(MessageSource messageSource, String errorKey) {
    return messageSource.getMessage(errorKey, null, Locale.ENGLISH);
  }

  private static String getErrorMessage(MessageSource messageSource, ConstraintViolation<?> constraint,
                                        String className) {
    String errorKey = getErrorCode(constraint, className);
    String errorMsg;
    try {
      errorMsg = messageSource.getMessage(errorKey, null, Locale.ENGLISH);
    } catch (Exception ex) {
      errorMsg = AppContextUtil.getApplicationContext().getMessage(errorKey, null, Locale.ENGLISH);
    }
    return errorMsg;
  }

  public static String getErrorMessage(String errorKey) {
    return messageSource.getMessage(errorKey, null, Locale.ENGLISH);
  }

  public static void addError(ValidationResult validationResult, String field, String errorMessageKey,
                              Object... msgFormatParameters) {
    String message = ValidationUtil.getErrorMessage(errorMessageKey);
    String errorMessage = message;
    if (msgFormatParameters != null && msgFormatParameters.length > 0) {
      errorMessage = String.format(message, msgFormatParameters);
    }
    DataValidationError ve = new DataValidationError(field, errorMessageKey, errorMessage);
    validationResult.addError(ve);
  }

  private static String getConstraintName(String messageTemplate) {
    String constraintName = "";

    if (messageTemplate != null && messageTemplate.toLowerCase().endsWith(".message}")) {
      int length = messageTemplate.length();
      int lastIndex = messageTemplate.lastIndexOf(".message}") - 1;
      int foundIndex = 0;
      for (int i = lastIndex; i < length; i--) {
        char character = messageTemplate.charAt(i);
        if (character == '.') {
          foundIndex = i;
          break;
        }
      }
      constraintName = messageTemplate.substring(foundIndex + 1, lastIndex + 1);
    }
    return constraintName;
  }

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    this.applicationContext = applicationContext;
    if (validator == null) {
      validator = (Validator) this.applicationContext.getBean("validator");
    }
    if (messageSource == null) {
      messageSource = (MessageSource) this.applicationContext.getBean("messageSource");
    }
  }

}