package edu.hbp.he.useractivity.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 */
@Component(value = "appContextUtil")
public class AppContextUtil implements ApplicationContextAware {
  private static ApplicationContext appContext = null;

  public AppContextUtil() {
    super();
  }

  @Override
  public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
    appContext = applicationContext;
  }

  public static ApplicationContext getApplicationContext() {
    return appContext;
  }

  public static Object getBean(String beanName) {
    return appContext.getBean(beanName);
  }
}
