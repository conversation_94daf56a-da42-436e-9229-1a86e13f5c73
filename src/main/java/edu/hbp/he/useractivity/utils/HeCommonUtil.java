package edu.hbp.he.useractivity.utils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;

import edu.hbp.he.useractivity.security.SecurityUtil;

/**
 * <AUTHOR>
 */
@Component
public class HeCommonUtil {
  private static final Logger logger = LoggerFactory.getLogger(HeCommonUtil.class);

  public static List<String> csvToList(String csv) {
    List<String> list = new ArrayList<>();
    if (csv != null && csv.trim().length() > 0) {
      if (csv.contains(",")) {
        list = Arrays.asList(csv.split(","));
      } else {
        list.add(csv);
      }
    }
    return list;
  }

  public static String asCsvFromList(List<String> items) {
    String csv = "";
    if (CollectionUtils.isNotEmpty(items)) {
      csv = Joiner.on(',').join(items);
    }
    return csv;
  }

  public static String getProductCoreId(String availabilityId) {
    String coreId = availabilityId;
    if (availabilityId != null && availabilityId.contains("-")) {
      int index = availabilityId.indexOf("-");
      coreId = availabilityId.substring(0, index);
      return coreId;
    }
    return coreId;
  }

  public static ArrayList<Long> asList(String values) {
    if (values != null) {
      ArrayList<Long> longValues = new ArrayList<Long>();
      String[] vals = values.split(",");
      for (String val : vals) {
        if (StringUtils.isNumeric(val)) {
          longValues.add(Long.parseLong(val));
        }
      }
      return longValues;
    }
    return null;
  }

  public static void logError(Logger logger, Exception exception) {
    String logging = "Anonymous";
    if (SecurityUtil.getCurrentUser() != null && SecurityUtil.getCurrentUser().getUsername() != null) {
      logging = SecurityUtil.getCurrentUser().getUserId() + " : " + SecurityUtil.getCurrentUser().getUsername();
    }
    StringBuilder sb = new StringBuilder(logging);
    sb.append(" : ").append(exception.getMessage());
    logger.error(sb.toString(), exception);
  }

  public static void logWarning(Logger logger, Exception exception) {
    String logging = "Anonymous";
    if (SecurityUtil.getCurrentUser() != null && SecurityUtil.getCurrentUser().getUsername() != null) {
      logging = SecurityUtil.getCurrentUser().getUserId() + " : " + SecurityUtil.getCurrentUser().getUsername();
    }
    StringBuilder sb = new StringBuilder(logging);
    sb.append(" : ").append(exception.getMessage());
    logger.warn(sb.toString(), exception);
  }

  public static boolean isAvailabilityId(String id) {
    boolean isAvailability = false;
    if (id != null && id.trim().length() > 0) {
      if (id.matches("[\\w]*-[a-zA-Z0-9]{3}-[a-zA-Z]{3}")) {
        isAvailability = true;
      }
    }
    return isAvailability;
  }
}
