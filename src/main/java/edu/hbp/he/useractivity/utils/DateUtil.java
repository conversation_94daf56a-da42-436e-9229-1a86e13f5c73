package edu.hbp.he.useractivity.utils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * 
 */
public class DateUtil {
  private static final Logger logger = LoggerFactory.getLogger(DateUtil.class);

  public static final String DATE_FORMAT_SHORT = "MMM dd, yyyy";
  public static final String DATE_FORMAT_MMM_YYYY = "MMM yyyy";
  public static final String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS_SSS = "yyyy-MM-dd HH:mm:ss.SSS";
  public static final String DATE_FORMAT_MM_DD_YYYY = "MM/dd/yyyy";


  public DateUtil() {
    super();
  }

  public static Date parseDate(String dateFormat, String date) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
    Date parsedDate = null;
    try {
      parsedDate = simpleDateFormat.parse(date);
    } catch (Exception ex) {
      logger.debug("Error occurred while parsing date: " + date);
    }
    return parsedDate;
  }

  public static Timestamp getCurrentDateAsTimestamp() {
    Date date = getCurrentDate();
    Timestamp ts = new Timestamp(date.getTime());
    return ts;
  }

  public static String parseString(String dateFormat, Date date) {
    SimpleDateFormat simpleDateFormat = new SimpleDateFormat(dateFormat);
    String parsedDate = null;
    try {
      parsedDate = simpleDateFormat.format(date);
    } catch (Exception ex) {
      logger.debug("Error occurred while parsing date: " + date);
    }
    return parsedDate;
  }

  public static Date getCurrentDate() {
    Date currentDate = Calendar.getInstance().getTime();
    return currentDate;
  }

  public static Date addYears(Date date, int years) {
    Calendar cal = Calendar.getInstance();
    cal.setTime(date);
    cal.add(Calendar.YEAR, years); //minus number would decrement the days
    return cal.getTime();
  }

  public static String getCurrentYearAsString() {
    Calendar cal = Calendar.getInstance();
    Integer year = cal.get(Calendar.YEAR);
    return year.toString();
  }
}
