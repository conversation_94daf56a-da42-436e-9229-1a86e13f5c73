package edu.hbp.he.useractivity.web.controller;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import edu.hbp.he.security.core.utils.WebUtil;
import edu.hbp.he.useractivity.domain.ItemAccessDetails;
import edu.hbp.he.useractivity.service.ItemAccessDetailsService;
import edu.hbp.he.useractivity.web.domain.RestResponseBody;
import edu.hbp.he.useractivity.web.validator.ItemAccessDetailsValidator;

@RequestMapping(value = "/api/useractivity")
@RestController
@CrossOrigin(originPatterns = "https://services*.hbsp.harvard.edu")
public class ItemAccessDetailsController {
  private static final String JSON_ACCEPT_HEADERS = "Accept=application/json";

  @Autowired
  private ItemAccessDetailsValidator itemAccessDetailsValidator;
  @Autowired
  private ItemAccessDetailsService itemAccessDetailsService;

  @RequestMapping(value = "/recently-viewed-items/products", method = RequestMethod.GET, headers = JSON_ACCEPT_HEADERS)
  public ResponseEntity<?> findItemAccessDetails() {
    List<ItemAccessDetails> product = itemAccessDetailsService.findItemAccessDetails("PRODUCT");
    RestResponseBody body = new RestResponseBody("recentlyViewedProducts", product);
    return WebUtil.getOkResponse(body);
  }

  @RequestMapping(value = "/recently-viewed-items/products", method = RequestMethod.POST, headers = JSON_ACCEPT_HEADERS)
  public ResponseEntity<?> saveItemAccessDetails(@RequestBody ItemAccessDetails itemAccessDetails) {
    itemAccessDetailsValidator.validateSaveItemAccessDetails(itemAccessDetails);
    itemAccessDetails.setItemType("PRODUCT");
    itemAccessDetailsService.saveItemAccessDetails(itemAccessDetails);
    RestResponseBody body = new RestResponseBody("data", "Successfully updated.");
    return WebUtil.getOkResponse(body);
  }
}
