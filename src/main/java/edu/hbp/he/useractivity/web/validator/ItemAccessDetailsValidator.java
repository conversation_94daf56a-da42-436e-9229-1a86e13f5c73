package edu.hbp.he.useractivity.web.validator;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import edu.hbp.he.useractivity.domain.ItemAccessDetails;
import edu.hbp.he.useractivity.exceptions.ValidationException;
import edu.hbp.he.useractivity.validation.ValidationResult;

@Component
public class ItemAccessDetailsValidator {

  public void validateSaveItemAccessDetails(ItemAccessDetails itemAccessDetails) {
    ValidationResult validationResult = new ValidationResult();
    if (StringUtils.isBlank(itemAccessDetails.getItemId())) {
      validationResult.addValidationError("itemId", "itemId is required");
    }

    if (StringUtils.isBlank(itemAccessDetails.getItemTitle())) {
      validationResult.addValidationError("itemTitle", "itemTitle is required");
    }

    if (validationResult.isHasErrors()) {
      ValidationException validationException = new ValidationException(validationResult);
      throw validationException;
    }
  }

}
