package edu.hbp.he.useractivity.web.domain;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonInclude;

import edu.hbp.he.useractivity.validation.DataValidationError;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonIgnoreProperties(ignoreUnknown = true)
public class RestResponseBody {

  private List<DataValidationError> errors = new ArrayList<>();
  private Map<String, Object> data = new HashMap<>();
  private String errorMessage;

  public RestResponseBody() {
  }

  public RestResponseBody(String key, Object value) {
    data.put(key, value);
  }

  public void setErrors(List<DataValidationError> errors) {
    this.errors = errors;
  }

  public void setValidationErrors(List<String> errors) {
    for (String error: errors) {
      DataValidationError validationError = new DataValidationError(error);
      this.errors.add(validationError);
    }
  }

  public void addValidationError(DataValidationError error) {
    errors.add(error);
  }

  public void addValidationError(String error) {
    DataValidationError validationError = new DataValidationError(error);
    this.errors.add(validationError);
  }

  public RestResponseBody putData(String key, Object value) {
    data.put(key, value);
    return this;
  }

  public List<DataValidationError> getErrors() {
    return errors;
  }

  public Map<String, Object> getData() {
    return data;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("RestResponseBody [errors=");
    builder.append(errors);
    builder.append(", data=");
    builder.append(data);
    builder.append(", errorMessage=");
    builder.append(errorMessage);
    builder.append("]");
    return builder.toString();
  }
}
