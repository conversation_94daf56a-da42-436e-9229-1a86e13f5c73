package edu.hbp.he.useractivity.repos;

import java.util.List;

import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

import edu.hbp.he.useractivity.domain.ItemAccessDetails;

public interface ItemAccessDetailsRepo extends JpaRepository<ItemAccessDetails, Long> {

  List<ItemAccessDetails> findByUserIdAndItemTypeOrderByItemAccessedDateDesc(Long userId, String itemType, Pageable pageable);

  List<ItemAccessDetails> findByUserIdAndItemTypeAndItemIdOrderByItemAccessedDateDesc(Long userId, String itemType, String itemId);

  List<ItemAccessDetails> findByUserIdAndItemType(Long userId, String itemType);
}
