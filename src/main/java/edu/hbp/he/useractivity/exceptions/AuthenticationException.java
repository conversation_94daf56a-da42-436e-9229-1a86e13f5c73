package edu.hbp.he.useractivity.exceptions;

import org.springframework.http.HttpStatus;
import org.springframework.security.authentication.BadCredentialsException;

public class AuthenticationException extends BadCredentialsException {
  private static final long serialVersionUID = -614860589506942568L;
  private HttpStatus httpStatus;

  public AuthenticationException(String message) {
    super(message);
  }

  public AuthenticationException(String message, Throwable throwable) {
    super(message, throwable);
  }

  public AuthenticationException(String message, HttpStatus httpStatus) {
    super(message);
    this.httpStatus = httpStatus;
  }

  public AuthenticationException(String message, Throwable throwable, HttpStatus httpStatus) {
    super(message, throwable);
    this.httpStatus = httpStatus;
  }

  public HttpStatus getHttpStatus() {
    return httpStatus;
  }

  public void setHttpStatus(HttpStatus httpStatus) {
    this.httpStatus = httpStatus;
  }
}
