package edu.hbp.he.useractivity.exceptions;

import java.io.IOException;
import java.security.InvalidKeyException;
import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;

import org.apache.commons.codec.EncoderException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.dao.DataAccessException;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.servlet.mvc.method.annotation.ResponseEntityExceptionHandler;

import edu.hbp.he.security.core.utils.WebUtil;
import edu.hbp.he.useractivity.validation.DataValidationError;
import edu.hbp.he.useractivity.web.domain.RestResponseBody;

/**
 * <AUTHOR>
 */
@ControllerAdvice
public class BaseExceptionHandler extends ResponseEntityExceptionHandler {

  private static final Logger logger = LoggerFactory.getLogger(BaseExceptionHandler.class);

  public BaseExceptionHandler() {
    super();
  }

  @ExceptionHandler(ValidationException.class)
  public ResponseEntity<RestResponseBody> handleValidationException(ValidationException ve) {
    RestResponseBody responseBody = new RestResponseBody();
    for (DataValidationError valError : ve.getValResult().getValidationErrorList()) {
      DataValidationError dve = new DataValidationError(valError.getFieldName(), valError.getErrorMessage(),
          valError.getErrorCode());
      responseBody.getErrors().add(dve);
    }
    return WebUtil.getBadRequestResponse(responseBody);
  }

  @ExceptionHandler(Exception.class)
  public ResponseEntity<RestResponseBody> handleException(Exception ex) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ex.getMessage());
    log(ex);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(NullPointerException.class)
  public ResponseEntity<RestResponseBody> handleNullPointerException(NullPointerException ne) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ne.getMessage());
    log(ne);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(RestServiceException.class)
  public ResponseEntity<RestResponseBody> handleServiceException(RestServiceException rse) {
    RestResponseBody responseBody = new RestResponseBody();
    if (!(rse.isResourceFound())) {
      responseBody.getErrors().add(new DataValidationError(rse.getMessage()));
      return WebUtil.getResourceNotFoundResponse(responseBody);
    }
    if (rse.isBadRequest()) {
      responseBody.getErrors().add(new DataValidationError(rse.getMessage()));
      return WebUtil.getBadRequestResponse(responseBody);
    }

    responseBody.setErrorMessage(rse.getMessage());
    log(rse);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(ResourceNotFoundException.class)
  public ResponseEntity<RestResponseBody> handleResourceNotFoundException(ResourceNotFoundException rnf) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(rnf.getMessage());
    log(rnf);
    return WebUtil.getResourceNotFoundResponse(responseBody);
  }
  
  @ExceptionHandler(RepoException.class)
  public ResponseEntity<RestResponseBody> handleRepoException(RepoException re) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(re.getMessage());
    log(re);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(DataAccessException.class)
  public ResponseEntity<RestResponseBody> handleDataAccessException(DataAccessException de) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(de.getMessage());
    log(de);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(IOException.class)
  public ResponseEntity<RestResponseBody> handleIOException(IOException ioe) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ioe.getMessage());
    log(ioe);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(InvalidKeyException.class)
  public ResponseEntity<RestResponseBody> handleInvalidKeyException(InvalidKeyException ike) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ike.getMessage());
    log(ike);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(BadPaddingException.class)
  public ResponseEntity<RestResponseBody> handleBadPaddingException(BadPaddingException bpe) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(bpe.getMessage());
    log(bpe);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(IllegalBlockSizeException.class)
  public ResponseEntity<RestResponseBody> handleIllegalBlockSizeException(IllegalBlockSizeException ibe) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ibe.getMessage());
    log(ibe);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(EncoderException.class)
  public ResponseEntity<RestResponseBody> handleEncoderException(EncoderException ee) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ee.getMessage());
    log(ee);
    return WebUtil.getInternalServerErrorResponse(responseBody);
  }

  @ExceptionHandler(ClientException.class)
  public ResponseEntity<RestResponseBody> handleClientException(ClientException ex) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ex.getMessage());
    responseBody.setErrors(ex.getErrors());
    log(ex);
    if (ex.getHttpStatus() != null) {
      return WebUtil.getResponse(responseBody, ex.getHttpStatus());
    } else {
      return WebUtil.getInternalServerErrorResponse(responseBody);
    }
  }

  @ExceptionHandler(AuthenticationException.class)
  public ResponseEntity<RestResponseBody> handleAuthenticationException(AuthenticationException ex) {
    RestResponseBody responseBody = new RestResponseBody();
    responseBody.setErrorMessage(ex.getMessage());
    if (ex.getHttpStatus() != null) {
      return WebUtil.getResponse(responseBody, ex.getHttpStatus());
    }
    return WebUtil.getUnauthorizedResponse(responseBody);
  }

  private void log(Exception exception) {
    // TODO uncomment
    // LogUtil.logError(logger, exception);
  }
}
