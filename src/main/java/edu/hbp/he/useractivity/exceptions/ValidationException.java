package edu.hbp.he.useractivity.exceptions;

import edu.hbp.he.useractivity.validation.ValidationResult;

/**
 * <AUTHOR>
 */
public class ValidationException extends RuntimeException {
  private static final long serialVersionUID = 1634258019904634527L;

  private ValidationResult valResult;

  public ValidationException() {
    super();
  }

  public ValidationException(String errorMessage) {
    super(errorMessage);
  }

  public ValidationException(ValidationResult valResult) {
    this.valResult = valResult;
  }

  public ValidationResult getValResult() {
    return valResult;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("ValidationException [valResult=");
    builder.append(valResult);
    builder.append("]");
    return builder.toString();
  }

}
