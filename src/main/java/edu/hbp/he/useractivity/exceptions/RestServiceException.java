package edu.hbp.he.useractivity.exceptions;

/**
 * <AUTHOR>
 */
public class RestServiceException extends RuntimeException {
  private static final long serialVersionUID = 6980891850839646674L;

  private boolean isResourceFound = true;
  private boolean badRequest = false;

  public RestServiceException() {
    super();
  }

  public RestServiceException(String errorMessage) {
    super(errorMessage);
  }

  public RestServiceException(String errorMessage, boolean isResourceFound) {
    super(errorMessage);
    this.isResourceFound = isResourceFound;
  }

  public boolean isResourceFound() {
    return isResourceFound;
  }

  public boolean isBadRequest() {
    return badRequest;
  }

  public void setBadRequest(boolean badRequest) {
    this.badRequest = badRequest;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("RestServiceException [isResourceFound=");
    builder.append(isResourceFound);
    builder.append(", badRequest=");
    builder.append(badRequest);
    builder.append("]");
    return builder.toString();
  }

}
