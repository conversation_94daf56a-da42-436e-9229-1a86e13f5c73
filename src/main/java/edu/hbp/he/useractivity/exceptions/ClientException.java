package edu.hbp.he.useractivity.exceptions;

import java.util.List;

import org.springframework.http.HttpStatus;

import edu.hbp.he.useractivity.validation.DataValidationError;

/**
 * <AUTHOR>
 */
public class ClientException extends RuntimeException {

  private static final long serialVersionUID = -4841022862183194542L;
  private HttpStatus httpStatus;
  private List<DataValidationError> errors;

  public ClientException() {
    super();
  }

  public ClientException(String errorMsg) {
    super(errorMsg);
  }

  public ClientException(HttpStatus httpStatus, String errorMsg, List<DataValidationError> errors) {
    super(errorMsg);
    this.httpStatus = httpStatus;
    this.errors = errors;
  }

  public HttpStatus getHttpStatus() {
    return httpStatus;
  }

  public void setHttpStatus(HttpStatus httpStatus) {
    this.httpStatus = httpStatus;
  }

  public List<DataValidationError> getErrors() {
    return errors;
  }

  public void setErrors(List<DataValidationError> errors) {
    this.errors = errors;
  }
}
