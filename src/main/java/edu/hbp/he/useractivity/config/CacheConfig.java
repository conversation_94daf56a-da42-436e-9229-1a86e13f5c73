package edu.hbp.he.useractivity.config;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.Cache;
import org.springframework.cache.annotation.CachingConfigurerSupport;
import org.springframework.cache.interceptor.CacheErrorHandler;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;

import edu.hbp.he.useractivity.props.CacheProps;
import edu.hbp.he.useractivity.utils.HeCommonUtil;

/**
 * Created by ramesh.rengasamy on 9/1/17.
 */
@Configuration
public class CacheConfig extends CachingConfigurerSupport {

  @Autowired
  private CacheProps properties;

  @Bean
  public RedisCacheManager cacheManager(RedisConnectionFactory connectionFactory) {
    RedisCacheManager redisCacheManager = RedisCacheManager.builder(connectionFactory)
        .cacheDefaults(cacheConfiguration(properties.getDefaultExpiration()))
        .withInitialCacheConfigurations(initialCacheConfigurations())
        .build();

    return redisCacheManager;
  }

  private RedisCacheConfiguration cacheConfiguration(long duration) {
    RedisCacheConfiguration redisCacheConfiguration = RedisCacheConfiguration.defaultCacheConfig()
        .disableCachingNullValues()
        .entryTtl(Duration.ofSeconds(duration));

    return redisCacheConfiguration;
  }

  private Map<String, RedisCacheConfiguration> initialCacheConfigurations() {
    Map<String, RedisCacheConfiguration> caches = new HashMap<>();
    properties.getExpiration().forEach((name, expiration) -> caches.put(name, cacheConfiguration(expiration)));
    return caches;
  }

  @Override
  public CacheErrorHandler errorHandler() {
    return new CacheErrorHandler() {
      private Logger logger = LoggerFactory.getLogger(CacheErrorHandler.class);

      @Override
      public void handleCacheGetError(RuntimeException exception, Cache cache, Object key) {
        HeCommonUtil.logError(logger, exception);
      }

      @Override
      public void handleCachePutError(RuntimeException exception, Cache cache, Object key, Object value) {
        HeCommonUtil.logError(logger, exception);
      }

      @Override
      public void handleCacheEvictError(RuntimeException exception, Cache cache, Object key) {
        HeCommonUtil.logError(logger, exception);

      }

      @Override
      public void handleCacheClearError(RuntimeException exception, Cache cache) {
        HeCommonUtil.logError(logger, exception);

      }
    };
  }
}
