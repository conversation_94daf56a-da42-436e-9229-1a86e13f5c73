package edu.hbp.he.useractivity.config;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.config.PropertyPlaceholderConfigurer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.support.ResourceBundleMessageSource;
import org.springframework.http.MediaType;
import org.springframework.http.converter.ByteArrayHttpMessageConverter;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.http.converter.xml.Jaxb2RootElementHttpMessageConverter;
import org.springframework.validation.Validator;
import org.springframework.validation.beanvalidation.LocalValidatorFactoryBean;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import com.fasterxml.jackson.databind.MapperFeature;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * <AUTHOR>
 */
@Configuration
public class CoreConfig implements WebMvcConfigurer {

  public CoreConfig() {
    super();
  }

  @Bean
  public Validator validator() {
    LocalValidatorFactoryBean lvfb = new LocalValidatorFactoryBean();
    return lvfb;
  }

  @Bean(name = "messageSource")
  public ResourceBundleMessageSource resourceBundleMessageSource() {
    ResourceBundleMessageSource rbms = new ResourceBundleMessageSource();
    String[] resourceLocations = {"errors/errors"};
    rbms.setBasenames(resourceLocations);
    return rbms;
  }

  @Bean
  public PropertyPlaceholderConfigurer propertyPlaceholderConfigurer() {
    PropertyPlaceholderConfigurer ppc = new PropertyPlaceholderConfigurer();
    ppc.setIgnoreUnresolvablePlaceholders(true);
    return ppc;
  }

  
  @Override
  public void configureMessageConverters(List<HttpMessageConverter<?>> converters) {
    converters.add(getByteArrayHttpMessageConverter());
    converters.add(getJsonConverter());
    converters.add(getFormHttpMessageConverter());
    converters.add(getStringConverter());
    converters.add(getXMLConverter());
    WebMvcConfigurer.super.configureMessageConverters(converters);
  }
  
  private FormHttpMessageConverter getFormHttpMessageConverter() {
    FormHttpMessageConverter converter = new FormHttpMessageConverter();
    MediaType mediaType = new MediaType("application", "x-www-form-urlencoded", StandardCharsets.UTF_8);
    converter.setSupportedMediaTypes(List.of(mediaType));
    return converter;
  }

  private MappingJackson2HttpMessageConverter getJsonConverter() {
    MappingJackson2HttpMessageConverter jsonConverter = new MappingJackson2HttpMessageConverter();
    MediaType jsonMediaType = MediaType.APPLICATION_JSON_UTF8;
    List<MediaType> jsonMediaTypes = new ArrayList<MediaType>();
    jsonMediaTypes.add(jsonMediaType);
    jsonConverter.setSupportedMediaTypes(jsonMediaTypes);
    ObjectMapper mapper = new ObjectMapper();
    mapper.disable(MapperFeature.DEFAULT_VIEW_INCLUSION);
    jsonConverter.setObjectMapper(mapper);
    return jsonConverter;
  }

  private StringHttpMessageConverter getStringConverter() {
    StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
    MediaType textHtmlMediaType = MediaType.TEXT_HTML;
    List<MediaType> textHtmlMediaTypes = new ArrayList<MediaType>();
    textHtmlMediaTypes.add(textHtmlMediaType);
    stringConverter.setSupportedMediaTypes(textHtmlMediaTypes);
    return stringConverter;
  }
  
  private ByteArrayHttpMessageConverter getByteArrayHttpMessageConverter() {
    ByteArrayHttpMessageConverter byteConverter = new ByteArrayHttpMessageConverter();
    MediaType octectMediaType = MediaType.APPLICATION_OCTET_STREAM;
    List<MediaType> mediaTypes = new ArrayList<MediaType>();
    mediaTypes.add(octectMediaType);
    byteConverter.setSupportedMediaTypes(mediaTypes);
    return byteConverter;
  }

  private Jaxb2RootElementHttpMessageConverter getXMLConverter() {
    Jaxb2RootElementHttpMessageConverter xmlConverter =  new Jaxb2RootElementHttpMessageConverter();
    MediaType xmlMediaType = MediaType.TEXT_XML;
    List<MediaType> xmlMediaTypes = new ArrayList<MediaType>();
    xmlMediaTypes.add(xmlMediaType);
    xmlConverter.setSupportedMediaTypes(xmlMediaTypes);
    return xmlConverter;
  }
}