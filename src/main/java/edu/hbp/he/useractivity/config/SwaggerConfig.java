package edu.hbp.he.useractivity.config;


import java.util.Arrays;
import java.util.Collections;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

import io.swagger.v3.oas.models.Components;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import io.swagger.v3.oas.models.security.SecurityRequirement;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.oas.models.servers.Server;

@Configuration
@Profile({"dev", "QA", "staging", "test"})
public class SwaggerConfig {
  @Bean
  public OpenAPI openAPI() {

    return new OpenAPI()
        .servers(Collections.singletonList(new Server().url("/")))
        .addSecurityItem(new SecurityRequirement().addList("bearer-jwt", Arrays.asList("read", "write")))
        .components(
            new Components()
                .addSecuritySchemes(
                    "bearer-jwt",
                    new SecurityScheme()
                        .name("bearer-jwt")
                        .type(SecurityScheme.Type.HTTP)
                        .scheme("bearer")
                        .bearerFormat("JWT")
                        .in(SecurityScheme.In.HEADER)
                )
        )
        .info(new Info().title("HE-User-Activity-V2 API Documentation")
            .description("Use this documentation as a reference to interact with HE-User-Activity-V2 API")
            .license(new License().name("Apache 2.0").url("http://springdoc.org")));
  }
}