package edu.hbp.he.useractivity.config;

import javax.sql.DataSource;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.orm.jpa.JpaTransactionManager;
import org.springframework.orm.jpa.JpaVendorAdapter;
import org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean;
import org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import jakarta.persistence.EntityManagerFactory;

@EnableJpaRepositories(basePackages = {"edu.hbp.he.useractivity.repos"})
@EnableTransactionManagement
@Configuration
public class DatabaseConfig {

  public DatabaseConfig() {
    super();
  }

  @Bean(name = "hb4eDataSource")
  @ConfigurationProperties(value = "spring.datasource-hb4e")
  @Primary
  public DataSource hb4eDataSource() {
    DataSource ds = DataSourceBuilder.create().build();
    return ds;
  }

  public JpaVendorAdapter hibernateJpaVendorAdapter() {
    HibernateJpaVendorAdapter vendorAdapter = new HibernateJpaVendorAdapter();
    vendorAdapter.setGenerateDdl(false);
    vendorAdapter.setShowSql(false);
    return vendorAdapter;
  }

  private String[] entityManagerScanPackages() {
    String[] scanPackages = {"edu.hbp.he.useractivity.domain"};
    return scanPackages;
  }

  @Bean
  public EntityManagerFactory entityManagerFactory() {
    LocalContainerEntityManagerFactoryBean factory = new LocalContainerEntityManagerFactoryBean();
    factory.setJpaVendorAdapter(hibernateJpaVendorAdapter());
    factory.setPackagesToScan(entityManagerScanPackages());
    factory.setDataSource(hb4eDataSource());
    factory.afterPropertiesSet();
    return factory.getObject();
  }

  @Bean(name = "transactionManager")
  public PlatformTransactionManager transactionManager() {
    JpaTransactionManager txManager = new JpaTransactionManager();
    txManager.setEntityManagerFactory(entityManagerFactory());
    return txManager;
  }
}
