package edu.hbp.he.useractivity.props;

import java.util.LinkedHashMap;
import java.util.Map;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Created by ramesh.rengasamy on 11/14/17.
 */
@Component
@RefreshScope
@ConfigurationProperties("cache")
public class CacheProps {
  /** Default Expiration. */
  private Integer defaultExpiration = 0;

  /** Cache Configuration Map. */
  private Map<String, Long> expiration = new LinkedHashMap<>();

  public Integer getDefaultExpiration() {
    return defaultExpiration;
  }

  public void setDefaultExpiration(final Integer defaultExpiration) {
    this.defaultExpiration = defaultExpiration;
  }

  public Map<String, Long> getExpiration() {
    return expiration;
  }

  public void setExpiration(final Map<String, Long> expiration) {
    this.expiration = expiration;
  }
}
