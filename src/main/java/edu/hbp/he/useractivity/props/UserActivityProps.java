package edu.hbp.he.useractivity.props;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@RefreshScope
@ConfigurationProperties("useractivity")
public class UserActivityProps {
  private Integer noOfRowsToReturn;

  public Integer getNoOfRowsToReturn() {
    return noOfRowsToReturn;
  }

  public void setNoOfRowsToReturn(Integer noOfRowsToReturn) {
    this.noOfRowsToReturn = noOfRowsToReturn;
  }
}
