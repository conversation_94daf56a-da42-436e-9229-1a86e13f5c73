package edu.hbp.he.useractivity.validation;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class DataValidationError implements Serializable {
  private static final long serialVersionUID = -7326319849713098418L;

  private String fieldName;
  private String errorCode;
  private String errorMessage;

  public DataValidationError() {
    super();
  }

  public DataValidationError(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  public DataValidationError(String fieldName, String errorMessage) {
    this.fieldName = fieldName;
    this.errorMessage = errorMessage;
  }

  public DataValidationError(String fieldName, String errorMessage, String errorCode) {
    this.fieldName = fieldName;
    this.errorMessage = errorMessage;
    this.errorCode = errorCode;
  }

  public String getFieldName() {
    return fieldName;
  }

  public void setFieldName(String fieldName) {
    this.fieldName = fieldName;
  }

  public String getErrorCode() {
    return errorCode;
  }

  public void setErrorCode(String errorCode) {
    this.errorCode = errorCode;
  }

  public String getErrorMessage() {
    return errorMessage;
  }

  public void setErrorMessage(String errorMessage) {
    this.errorMessage = errorMessage;
  }

  @Override
  public String toString() {
    StringBuilder builder = new StringBuilder();
    builder.append("DataValidationError [fieldName=");
    builder.append(fieldName);
    builder.append(", errorCode=");
    builder.append(errorCode);
    builder.append(", errorMessage=");
    builder.append(errorMessage);
    builder.append("]");
    return builder.toString();
  }

}
