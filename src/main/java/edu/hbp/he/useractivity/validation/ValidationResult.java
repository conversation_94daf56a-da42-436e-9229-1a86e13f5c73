package edu.hbp.he.useractivity.validation;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 */
public class ValidationResult implements Serializable {

  private static final long serialVersionUID = 4336702134128550614L;

  private List<DataValidationError> validationErrorList = new ArrayList<DataValidationError>();
  private boolean hasErrors = false;

  public ValidationResult() {
    super();
  }

  public boolean hasNoErrors() {
    return !isHasErrors();
  }

  public boolean isHasErrors() {
    if (this.validationErrorList == null || this.validationErrorList.isEmpty()) {
      hasErrors = false;
    } else {
      hasErrors = true;
    }
    return hasErrors;
  }

  public void setHasErrors(boolean hasErrors) {
    this.hasErrors = hasErrors;
  }

  public List<DataValidationError> getValidationErrorList() {
    return validationErrorList;
  }

  public void setValidationErrorList(List<DataValidationError> validationErrorList) {
    this.validationErrorList = validationErrorList;
    if (validationErrorList != null && validationErrorList.size() > 0) {
      this.hasErrors = true;
    }
  }

  public void addErrorList(List<DataValidationError> validationErrors) {
    this.validationErrorList.addAll(validationErrors);
    this.hasErrors = true;
  }

  public void addValidationError(String field, String errorMessage) {
    DataValidationError validationError = new DataValidationError(field, errorMessage);
    this.addError(validationError);
  }

  public void addError(DataValidationError validationError) {
    this.validationErrorList.add(validationError);
    this.hasErrors = true;
  }

  /**
   * This method is needed for backward compatibility only
   */
  public List<String> getErrorList() {
    List<String> errorList = new ArrayList<String>();
    for (DataValidationError error : validationErrorList) {
      errorList.add(error.getErrorMessage());
    }
    return errorList;
  }

  /**
   * This method is needed for backward compatibility only
   */
  public void addErrorMessage(String errorMessage) {
    if (StringUtils.isNotEmpty(errorMessage)) {
      DataValidationError error = new DataValidationError();
      error.setErrorMessage(errorMessage);
      this.validationErrorList.add(error);
    }
  }

  @Override
  public String toString() {
    return "ValidationResult [validationErrorList=" + validationErrorList
        + ", hasErrors=" + hasErrors + "]";
  }

}
