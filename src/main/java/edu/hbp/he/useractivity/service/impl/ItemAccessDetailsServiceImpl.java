package edu.hbp.he.useractivity.service.impl;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import edu.hbp.he.useractivity.domain.ItemAccessDetails;
import edu.hbp.he.useractivity.props.UserActivityProps;
import edu.hbp.he.useractivity.repos.ItemAccessDetailsRepo;
import edu.hbp.he.useractivity.security.SecurityUtil;
import edu.hbp.he.useractivity.service.ItemAccessDetailsService;
import edu.hbp.he.useractivity.utils.DateUtil;

@Service
public class ItemAccessDetailsServiceImpl implements ItemAccessDetailsService {

  @Autowired
  private ItemAccessDetailsRepo itemAccessDetailsRepo;
  @Autowired
  private UserActivityProps userActivityProps;

  @Override
  public void saveItemAccessDetails(ItemAccessDetails itemAccessDetails) {
    List<ItemAccessDetails> itemAccessDetailsList = itemAccessDetailsRepo.findByUserIdAndItemTypeAndItemIdOrderByItemAccessedDateDesc(
        SecurityUtil.getCurrentUser().getUserId(),
        itemAccessDetails.getItemType(),
        itemAccessDetails.getItemId());

    if (CollectionUtils.isEmpty(itemAccessDetailsList)) {
      itemAccessDetails.setUserId(SecurityUtil.getCurrentUser().getUserId());
      itemAccessDetails.setCreatedDate(DateUtil.getCurrentDateAsTimestamp());
      itemAccessDetails.setItemAccessedDate(DateUtil.getCurrentDateAsTimestamp());
      itemAccessDetailsRepo.save(itemAccessDetails);
    } else {
      ItemAccessDetails existingItemAccessDetails = itemAccessDetailsList.get(0);
      existingItemAccessDetails.setItemTitle(itemAccessDetails.getItemTitle());
      existingItemAccessDetails.setItemAccessedDate(DateUtil.getCurrentDateAsTimestamp());
      itemAccessDetailsRepo.save(existingItemAccessDetails);
    }
  }

  @Override
  public List<ItemAccessDetails> findItemAccessDetails(String itemType) {
    PageRequest pageRequest = PageRequest.of(0, userActivityProps.getNoOfRowsToReturn());
    Long userId = SecurityUtil.getCurrentUser().getUserId();
    return itemAccessDetailsRepo.findByUserIdAndItemTypeOrderByItemAccessedDateDesc(userId, itemType, pageRequest);
  }

  @Override
  public List<ItemAccessDetails> findItemAccessDetails(String itemType, String itemId) {
    Long userId = SecurityUtil.getCurrentUser().getUserId();
    return itemAccessDetailsRepo.findByUserIdAndItemTypeAndItemIdOrderByItemAccessedDateDesc(userId, itemType, itemId);
  }
}
