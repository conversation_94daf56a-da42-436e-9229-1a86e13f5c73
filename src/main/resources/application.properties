# ----------------------------------------------------------------
# DO NOT move this file into config server
# This file will be processed by build.gradle to populate group, version and name
# This file should contain only the properties needed at build time
# ----------------------------------------------------------------
#info endpoint
 info.build.group=${group}
 info.build.version=${version}
 info.build.baseName=${war.baseName}
# ----------------------------------------------------------------
# End of File
# ----------------------------------------------------------------
# org.springframework.core.codec, org.springframework.http, org.springframework.web
logging.level.root=WARN
# logging.level.org.springframework.web=TRACE
# logging.level.org.springframework.jdbc.core=TRACE
# logging.level.org.hibernate.SQL=TRACE
# logging.level.org.hibernate=ERROR