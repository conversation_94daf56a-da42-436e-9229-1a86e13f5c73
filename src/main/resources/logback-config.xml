<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>
<included>
     <springProfile name="test">
        <appender name="SPLUNK" class="com.splunk.logging.HttpEventCollectorLogbackAppender">
            <source>hb4e-user-activity-v2.test</source>
            <sourcetype>logback</sourcetype>
            <url>https://http-inputs-harvardbusiness.splunkcloud.com</url>
            <token>CDD78477-35FD-59F6-89B5-28AC99CFB082</token>
            <disableCertificateValidation>true</disableCertificateValidation>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%t] %-40.40logger{39} : %m%n%wex</pattern>
            </layout>
        </appender>
        <logger name="org.springframework" level="ERROR" />
        <logger name="org.springframework.boot.actuate.audit" level="INFO" />
        <root level="INFO">
            <appender-ref ref="SPLUNK" />
        </root>
    </springProfile>
    <springProfile name="QA">
        <appender name="SPLUNK" class="com.splunk.logging.HttpEventCollectorLogbackAppender">
            <source>hb4e-user-activity-v2.qa</source>
            <sourcetype>logback</sourcetype>
            <url>https://http-inputs-harvardbusiness.splunkcloud.com</url>
            <token>CDD78477-35FD-59F6-89B5-28AC99CFB082</token>
            <disableCertificateValidation>true</disableCertificateValidation>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%t] %-40.40logger{39} : %m%n%wex</pattern>
            </layout>
        </appender>
        <logger name="org.springframework" level="ERROR" />
        <logger name="org.springframework.boot.actuate.audit" level="INFO" />
        <root level="INFO">
            <appender-ref ref="SPLUNK" />
        </root>
    </springProfile>
    <springProfile name="prod">
        <appender name="SPLUNK" class="com.splunk.logging.HttpEventCollectorLogbackAppender">
            <source>hb4e-user-activity-v2.prod</source>
            <sourcetype>logback</sourcetype>
            <url>https://http-inputs-harvardbusiness.splunkcloud.com</url>
            <token>B3FEA860-F949-521A-A95F-FB0A96FB1B41</token>
            <disableCertificateValidation>true</disableCertificateValidation>
            <layout class="ch.qos.logback.classic.PatternLayout">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} %5p --- [%t] %-40.40logger{39} : %m%n%wex</pattern>
            </layout>
        </appender>
        <logger name="org.springframework" level="ERROR" />
        <logger name="org.springframework.boot.actuate.audit" level="INFO" />
        <root level="INFO">
            <appender-ref ref="SPLUNK" />
        </root>
    </springProfile>
    <root level="INFO">
        <appender-ref ref="CONSOLE" />        
    </root>
</included>
