@Library('hbsp-core') _

pipeline {
  agent any

  tools {
    jdk 'Java_21'
  }

  options {
    disableConcurrentBuilds()
  }

  environment {
    artifact = 'hb4e-user-activity-v2'
  }

  stages {
    stage('Build') {
      steps {
        script {
          withCredentials([usernamePassword(credentialsId: 'nexus-credentials-id', passwordVariable: 'NEXUS_PWD', usernameVariable: 'NEXUS_USER')]) {
            sh './gradlew clean build -Dorg.gradle.authentication.username=$NEXUS_USER -Dorg.gradle.authentication.password=$NEXUS_PWD'
          }
        }
      }
    }

    stage('Checkstyle Analysis') {
      steps {
        sh "./gradlew checkstyleMain"
      }
    }

    stage('CheckMarx Analysis') {
      steps {
        script {
          checkmarx.scan('hb4e-user-activity',"${env.BRANCH_NAME}",'HE',"!**/build/**",5)
        }
      }
    }

    stage('Sonar Analysis') {
      tools {
        jdk "Java_21" // Setting JDK 11 for SonarQube version
      }
      steps {
        sh "./gradlew sonarqube -Dsonar.projectKey=org.hew.services:hb4e-user-activity-v2 -Dsonar.sources=src/main/java -Dsonar.java.binaries=build/classes/java/main/ -Dsonar.login=****************************************"
      }
    }

    stage('Login to AWS ECR') {
      when {
        expression {
          return isReleaseBuild()
        }
      }

      steps {
        sh "aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 964020329682.dkr.ecr.us-east-1.amazonaws.com"
      }
    }

    stage('Docker build') {
      when {
        expression {
          return isReleaseBuild()
        }
      }

      environment {
        version = sh script: "./gradlew -q getVersion", returnStdout: true
      }

      steps {
        sh "docker build -t 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:$version -t 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:latest --build-arg version=$version -f docker/Dockerfile ."
      }
    }

    stage('Push the image to AWS ECR') {
      when {
        expression {
          return isReleaseBuild()
        }
      }

      environment {
        version = sh script: "./gradlew -q getVersion", returnStdout: true
      }

      steps {
        sh "docker push 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:$version"
        sh "docker push 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:latest"
      }
    }

    stage('Update the service in TEST env') {
      when {
        expression {
          return isReleaseBuild()
        }
      }

      steps {
        sh "aws ecs update-service --cluster he-test-cluster --service he-test-hb4e-user-activity-v2-service --force-new-deployment"
      }
    }
  }

  post {
    failure {
      /* notify users when the Pipeline fails */
      mail to: '<EMAIL> <EMAIL>',
          subject: "Failed Pipeline: ${currentBuild.fullDisplayName}",
          body: "Something is wrong with ${env.BUILD_URL}"
    }
  }
}

def isReleaseBuild() {
  return env.BRANCH_NAME.contains('release-')
}
