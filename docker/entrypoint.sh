#!/bin/bash
set -e
ENV=$1
GC=-XX:+UseG1GC 
TIMEZONE=-Duser.timezone=UTC
JAVA_TOOL_OPTIONS=-agentlib:jdwp=transport=dt_socket,address=5005,server=y,suspend=n

if [ "$ENV" = "dev" ]
then
    echo java ${JAVA_OPTS} ${JAVA_TOOL_OPTIONS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=dev
    exec java ${JAVA_OPTS} ${JAVA_TOOL_OPTIONS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=dev
elif [ "$ENV" = "test" ]
then
    echo java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=test
    exec java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=test
elif [ "$ENV" = "QA" ]
then
    echo java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=QA
    exec java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=QA
elif [ "$ENV" = "prod" ]
then
    echo java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=prod
    exec java ${JAVA_OPTS} ${GC} ${TIMEZONE} -jar hb4e-user-activity-v2.war --spring.profiles.active=prod
elif [ "$ENV" = "help" ]
then
    echo "You must pass the one of these parameter. dev, test, QA or prod."
else
    exec "$@"
fi

