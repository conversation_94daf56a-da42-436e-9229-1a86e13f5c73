# Introduction
The following document explains the steps to build, run, and push image to aws ecr repository.

## Build the war file
``` groovy
./gradlew clean build
```

# Build and run the docker image.
## Option 1: Using `docker build` and `docker container run` to build and run the docker image.
```
To enable BuildKit, set this environment variable. 
$ export DOCKER_BUILDKIT=1

Set the release version
$ export version=2.0.27

Build the docker image
$ docker build -t 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:$version --build-arg version=$version -f ./docker/Dockerfile .

Run the docker image
$ docker container run --rm -it -v ~/.aws:/root/.aws -p 8080:8080 964020329682.dkr.ecr.us-east-1.amazonaws.com/he/hb4e-user-activity-v2:$version test

Verify the container is up and runnin
$ curl http://localhost:8080/admin/info
```

## Option 2: Using `docker-compose` to build and run the docker image.
```
Set the release version
$ export version=2.0.27

Build the docker image
$ docker-compose build

Run the container in dev profile
$ docker-compose -f docker/docker-compose.yml -f docker/docker-compose.override.yml  up 

Run the container in test profile
$ docker-compose -f docker/docker-compose.yml -f docker/docker-compose.test.yml  up 

Run the container in QA profile
$ docker-compose -f docker-compose.yml -f docker-compose.qa.yml up
```

# Troubleshooting
## If you see an error in container about resolving hosts/services. This is usually happens when container tries to resolve hosts which are available thru VPN.
## Option 1:
```
Go to Docker > Preferences > Docker Engine -> enter the following and restart.
"bip" : "*************/24"
```

## Option 2: Reset to factory default and apply Option 1.
```
Go to Docker > Troubleshoot > Reset to factory defaults.
```

## Option 3: Remove unused images, networks, etc
```
$ docker system prune
```
