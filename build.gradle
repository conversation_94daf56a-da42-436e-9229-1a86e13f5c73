plugins {
    id "java"
    id "war"
    id "eclipse"
    id "idea"
    id "org.springframework.boot" version "3.5.4"
    id "io.spring.dependency-management" version "1.1.0"
    id "com.gorylenko.gradle-git-properties" version "2.4.2"
    id "org.sonarqube" version "4.0.0.2929"
    id "checkstyle"
}

group = "edu.hbp.he"
version = "2.0.1"

springBoot {
    buildInfo()
}

war {
    archiveBaseName = "hb4e-user-activity-v2"
}

def nexusRepos = [
        "https://nexus.hbsp.harvard.edu/content/groups/public",
        "https://nexus.hbsp.harvard.edu/content/repositories/releases",
        "https://nexus.hbsp.harvard.edu/content/repositories/thirdparty",
        "https://nexus.hbsp.harvard.edu/content/repositories/central"
]

repositories {
    mavenLocal()
    mavenCentral()
    maven { url "https://splunk.artifactoryonline.com/ext-releases-local" }
    maven { url "https://splunk.jfrog.io/splunk/ext-releases-local" }
    maven { url "https://repo2.maven.org/maven2" }
    maven { url "https://repo1.maven.org/maven2" }
    maven { url "https://repo.spring.io/snapshot" }
    maven { url "https://repo.spring.io/milestone" }

    nexusRepos.each { repoUrl ->
        maven {
            credentials {
                username = System.getenv("NEXUS_USER")
                password = System.getenv("NEXUS_PWD")
            }
            url uri(repoUrl)
        }
    }
}

dependencies {
    //-->HBP Dependencies
    implementation("edu.hbp.he:hb4e-security-autoconfigure-v2:1.0.7")

    //-->JavaX Dependencies
//  implementation("javax.el:el-api:2.2")

    //-->JUnit And Other Test Dependencies
    testImplementation("junit:junit:4.13.2")
    testImplementation("org.mockito:mockito-core:5.19.0")

    //-->Apache Dependencies
    implementation("commons-logging:commons-logging:1.3.5")
//  implementation("commons-beanutils:commons-beanutils:1.11.0")
    implementation("commons-collections:commons-collections:3.2.2")
//  implementation("commons-lang:commons-lang:2.6")
//  implementation("commons-httpclient:commons-httpclient:3.1")
//  implementation("commons-net:commons-net:3.12.0")
//  implementation("commons-validator:commons-validator:1.10.0")
//  implementation("org.apache.commons:commons-io:1.3.2")
//  implementation("org.apache.commons:commons-pool2:2.12.1")
    implementation("commons-codec:commons-codec:1.19.0")
    implementation("org.apache.commons:commons-lang3:3.18.0")

    //-->Jackson and JSON Dependencies
    implementation("com.fasterxml.jackson.core:jackson-databind:2.19.2")
    implementation("com.fasterxml.jackson.core:jackson-core:2.19.2")
    implementation("com.fasterxml.jackson.core:jackson-annotations:2.19.2")
    implementation("com.fasterxml.jackson.dataformat:jackson-dataformat-xml:2.19.2")
//  implementation("org.json:json:20250517")

    //-->MySQL and Connection Pool Dependencies
    runtimeOnly("com.mysql:mysql-connector-j:9.4.0")

    //-->Hibernate Dependencies
    implementation 'org.hibernate.orm:hibernate-core:6.6.22.Final'
    implementation 'jakarta.persistence:jakarta.persistence-api:3.1.0'
    implementation 'jakarta.validation:jakarta.validation-api:3.0.2'

    //-->Redis Cache Dependencies
    implementation("redis.clients:jedis:6.1.0")

    //-->Google Dependencies
    implementation("com.google.guava:guava:33.4.6-jre")

    //-->Springframework Dependencies
    implementation("org.springframework.boot:spring-boot-starter") {
        exclude(module: "commons-logging")
    }
    implementation("org.springframework.boot:spring-boot")
    implementation("org.springframework.boot:spring-boot-autoconfigure")
    implementation("org.springframework.boot:spring-boot-starter-logging")
    implementation("org.springframework.boot:spring-boot-starter-web")
    implementation("org.springframework.boot:spring-boot-starter-jdbc")
    implementation("org.springframework.boot:spring-boot-configuration-processor")
    implementation("org.springframework.cloud:spring-cloud-starter-config:4.3.0")
    implementation("org.springframework.boot:spring-boot-starter-actuator")
    implementation("org.springframework.security.oauth:spring-security-oauth2:2.5.2.RELEASE")
    implementation("org.springframework.boot:spring-boot-starter-data-jpa") {
        exclude(module: "hibernate-entitymanager")
    }
    implementation("org.springframework.boot:spring-boot-starter-security")
    implementation("org.springframework.boot:spring-boot-starter-cache")
    implementation("org.springframework.boot:spring-boot-starter-data-redis")
    implementation("org.springframework.boot:spring-boot-starter-hateoas")
    implementation("org.springframework.hateoas:spring-hateoas:2.4.1")
    implementation("org.springframework.data:spring-data-redis")

    testImplementation("org.springframework.boot:spring-boot-starter-test") {
        exclude(module: "commons-logging")
    }
    runtimeOnly("org.springframework.boot:spring-boot-starter-tomcat")
    //dev("org.springframework.boot:spring-boot-devtools")//use dev tool on local

    //-->Splunk Dependencies
    runtimeOnly("com.splunk.logging:splunk-library-javalogging:1.11.8")
    implementation("org.jetbrains.kotlin:kotlin-stdlib:2.2.10")

    //-->Amazon Dependencies
//  implementation("software.amazon.awssdk:s3:2.31.77")
//  implementation("software.amazon.awssdk:aws-core:2.31.77")
//  implementation("software.amazon.awssdk:sns:2.31.77")

    //-->Swagger Dependencies
    implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9")

    //Others Dependencies
    implementation("org.codehaus.woodstox:woodstox-core-asl:4.4.1")
//  implementation("org.glassfish.web:el-impl:2.2.1-b05")
//  implementation("com.jayway.jsonpath:json-path:2.9.0")

    //-->New Relic Dependencies
    implementation('com.newrelic.agent.java:agent-bridge:8.23.0')
    implementation('com.newrelic.agent.java:agent-bridge-datastore:8.23.0')
    implementation('com.newrelic.agent.java:newrelic-weaver-api:8.23.0')
    implementation('com.newrelic.agent.java:newrelic-weaver-scala-api:8.23.0')
}

dependencyAnalysis {
    issues {
        all {
            onAny {
                severity("fail")
            }
        }
    }
}

configurations.configureEach {
    exclude group: "com.hbp.integration"
}

configurations {
    all*.exclude module: "log4j-slf4j-impl"
    all*.exclude module: "commons-logging"
}

sourceSets {
    main {}
}

checkstyle {
    configFile = file("${project.rootDir}/config/checkstyle/checkstyle.xml")
    toolVersion = "10.13.0"
}

task getVersion() {
    doLast { print version }
}
